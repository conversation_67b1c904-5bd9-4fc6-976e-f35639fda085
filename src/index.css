@tailwind base;
@tailwind components;
@tailwind utilities;

/* Triagentic AI Design System - Medical AI Platform Brand Colors
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Brand Colors - Triagentic AI */
    --brand-white: 240 100% 98.8%;
    --brand-light-blue: 230 45% 58%;
    --brand-purple: 250 35% 54%;
    --brand-deep-purple: 282 40% 46%;
    --brand-magenta: 304 46% 45%;

    /* Core System Colors */
    --background: 240 100% 98.8%;
    --foreground: 240 10% 15%;

    --card: 240 100% 98.8%;
    --card-foreground: 240 10% 15%;

    --popover: 240 100% 98.8%;
    --popover-foreground: 240 10% 15%;

    --primary: 230 45% 58%;
    --primary-foreground: 240 100% 98.8%;

    --secondary: 240 15% 95%;
    --secondary-foreground: 240 10% 25%;

    --muted: 240 15% 95%;
    --muted-foreground: 240 8% 50%;

    --accent: 250 35% 54%;
    --accent-foreground: 240 100% 98.8%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 240 100% 98.8%;

    --border: 240 15% 90%;
    --input: 240 15% 92%;
    --ring: 230 45% 58%;

    /* Medical AI Gradients */
    --gradient-hero: linear-gradient(135deg, hsl(var(--brand-light-blue)), hsl(var(--brand-purple)));
    --gradient-feature: linear-gradient(135deg, hsl(var(--brand-purple)), hsl(var(--brand-deep-purple)));
    --gradient-cta: linear-gradient(135deg, hsl(var(--brand-deep-purple)), hsl(var(--brand-magenta)));
    --gradient-subtle: linear-gradient(180deg, hsl(var(--background)), hsl(var(--secondary)));

    /* Medical Professional Shadows */
    --shadow-professional: 0 4px 20px hsl(var(--brand-light-blue) / 0.15);
    --shadow-card: 0 2px 10px hsl(var(--brand-purple) / 0.1);
    --shadow-feature: 0 8px 30px hsl(var(--brand-deep-purple) / 0.2);

    /* Typography Scale for Medical Content */
    --font-medical-hero: 3.5rem;
    --font-medical-heading: 2.5rem;
    --font-medical-subheading: 1.5rem;
    --font-medical-body: 1.125rem;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}